#!/bin/bash

# Development startup script for GeeklyNews Hugo site

echo "🚀 Starting GeeklyNews development server..."

# Check if <PERSON> is installed
if ! command -v hugo &> /dev/null; then
    echo "❌ <PERSON> is not installed. Please install <PERSON> first:"
    echo "   macOS: brew install hugo"
    echo "   Windows: choco install hugo-extended"
    echo "   Linux: snap install hugo --channel=extended"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first:"
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Start Hugo development server
echo "🌐 Starting Hugo server at http://localhost:1313"
echo "📝 Press Ctrl+C to stop the server"
echo ""

hugo server --disableFastRender --navigateToChanged --bind 0.0.0.0 --port 1313
