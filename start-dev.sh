#!/bin/bash

# Development startup script for GeeklyNews Hugo site

echo "🚀 Starting GeeklyNews development server..."

# Check if <PERSON> is installed
if ! command -v hugo &> /dev/null; then
    echo "❌ Hugo is not installed."
    echo ""
    echo "📦 To install Hugo Extended:"
    echo ""
    echo "🍎 macOS:"
    echo "   brew install hugo"
    echo ""
    echo "🪟 Windows:"
    echo "   choco install hugo-extended"
    echo "   # Or download from: https://github.com/gohugoio/hugo/releases"
    echo ""
    echo "🐧 Linux:"
    echo "   snap install hugo --channel=extended"
    echo "   # Or: sudo apt install hugo (may be older version)"
    echo ""
    echo "📖 More installation options:"
    echo "   https://gohugo.io/installation/"
    echo ""
    echo "⚠️  Make sure to install Hugo Extended (not the standard version)"
    exit 1
fi

# Check Hugo version
HUGO_VERSION=$(hugo version 2>/dev/null | head -n1)
echo "✅ Found Hugo: $HUGO_VERSION"

# Check if it's Hugo Extended
if ! hugo version | grep -q "extended"; then
    echo "⚠️  Warning: You may need <PERSON> Extended for full theme support"
    echo "   Some features might not work with the standard Hugo version"
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first:"
    echo "   Visit: https://nodejs.org/"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing Node.js dependencies..."
    npm install
fi

# Start Hugo development server
echo "🌐 Starting Hugo server at http://localhost:1313"
echo "📝 Press Ctrl+C to stop the server"
echo ""

hugo server --config ./hugo.toml --disableFastRender --navigateToChanged --bind 0.0.0.0 --port 1313
