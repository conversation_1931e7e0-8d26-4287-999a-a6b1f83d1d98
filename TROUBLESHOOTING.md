# Troubleshooting Guide

## Common Issues and Solutions

### 1. "Unable to locate config file or config directory"

**Problem**: This error occurs when <PERSON> can't find the configuration file or <PERSON> is not installed.

**Solutions**:

**Option A: Use explicit config flag (RECOMMENDED)**
```bash
hugo server --config ./hugo.toml
# or
npm run dev  # (now includes the config flag)
```

**Option B: Install Hugo if not installed**
- **macOS (Homebrew)**: `brew install hugo`
- **Windows (Chocolatey)**: `choco install hugo-extended`
- **Linux (Snap)**: `snap install hugo --channel=extended`

**Option C: Check you're in the correct directory**
Make sure you're in the project root directory that contains `hugo.toml`:
```bash
ls -la hugo.toml  # Should exist and be readable
```

**Option D: Use Docker (no Hugo installation required)**
```bash
docker-compose up
```

### 2. "npm run dev" fails

**Problem**: The development command fails to start.

**Solutions**:
1. Make sure all dependencies are installed:
   ```bash
   npm install
   ```

2. Build CSS first:
   ```bash
   npm run build:css:once
   ```

3. Try running commands separately:
   ```bash
   # Terminal 1 - CSS watching
   npm run dev:css-only
   
   # Terminal 2 - Hugo server
   hugo server -D
   ```

### 3. CSS not loading or styles not applied

**Problem**: The website loads but styling is missing.

**Solutions**:
1. Build CSS manually:
   ```bash
   npm run build:css:once
   ```

2. Check if `static/css/style.css` exists and has content

3. Clear browser cache and reload

4. Make sure Tailwind is processing correctly:
   ```bash
   npx tailwindcss -i ./assets/css/main.css -o ./static/css/style.css
   ```

### 4. "Module not found" errors

**Problem**: npm packages are missing.

**Solutions**:
1. Delete node_modules and reinstall:
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

2. Make sure you're using Node.js v18 or later:
   ```bash
   node --version
   ```

### 5. Hugo version compatibility issues

**Problem**: Hugo version is too old or incompatible.

**Solutions**:
1. Check Hugo version:
   ```bash
   hugo version
   ```

2. Update Hugo to v0.121.0 or later:
   ```bash
   # Using npm
   npm install -g hugo-extended@latest
   
   # Using Homebrew (macOS)
   brew upgrade hugo
   
   # Using Chocolatey (Windows)
   choco upgrade hugo-extended
   ```

### 6. Port 1313 already in use

**Problem**: Hugo can't start because port 1313 is occupied.

**Solutions**:
1. Use a different port:
   ```bash
   hugo server -D --port 1314
   ```

2. Kill the process using port 1313:
   ```bash
   # Find the process
   lsof -i :1313
   
   # Kill it (replace PID with actual process ID)
   kill -9 PID
   ```

### 7. Images not displaying

**Problem**: Images in posts or layouts don't show up.

**Solutions**:
1. Make sure images are in the `static/images/` directory
2. Use correct paths in markdown: `/images/filename.jpg`
3. Check image file permissions
4. Verify image file extensions match the references

### 8. Dark mode not working

**Problem**: Dark mode toggle doesn't work.

**Solutions**:
1. Make sure Alpine.js is loaded (check browser console)
2. Clear localStorage:
   ```javascript
   localStorage.removeItem('darkMode')
   ```
3. Check if JavaScript is enabled in your browser

### 9. JSON Layout Warning

**Problem**: You see this warning during build:
```
WARN found no layout file for "JSON" for kind "home"
```

**Solution**: This warning has been fixed in the latest version. The theme now includes the required JSON layout file at `themes/coveredgeekly/layouts/_default/index.json`. If you still see this warning:

1. Make sure you have the latest theme files
2. Check that `themes/coveredgeekly/layouts/_default/index.json` exists
3. The warning is harmless and won't affect site functionality

### 10. Search functionality not working

**Problem**: Search doesn't return results.

**Solutions**:
1. Make sure `layouts/_default/index.json` exists
2. Check if `/index.json` is accessible in your browser
3. Verify posts have the correct front matter
4. Clear browser cache

### 10. Build fails on Cloudflare Pages

**Problem**: Deployment fails during build.

**Solutions**:
1. Check build settings:
   - Build command: `npm run production`
   - Build output directory: `public`
   - Node.js version: `18`

2. Make sure all dependencies are in `package.json`

3. Check build logs for specific error messages

## Getting Help

If you're still experiencing issues:

1. **Check the Hugo documentation**: https://gohugo.io/documentation/
2. **Check Tailwind CSS documentation**: https://tailwindcss.com/docs
3. **Search existing issues** in the repository
4. **Create a new issue** with:
   - Your operating system
   - Node.js version (`node --version`)
   - Hugo version (`hugo version`)
   - Complete error message
   - Steps to reproduce the issue

## Quick Setup Script

For automated setup, run:
```bash
./setup.sh
```

This script will:
- Check Node.js version
- Install npm dependencies
- Build CSS
- Check for Hugo installation
- Provide next steps
