# CoveredGeekly Hugo Theme

A modern, responsive Hugo theme inspired by CoveredGeekly, optimized for viral news and entertainment content with a focus on Facebook engagement and social media traffic.

## Features

- 🚀 **Optimized for Social Media**: Facebook Open Graph, Twitter Cards, and social sharing buttons
- 📱 **Mobile-First Design**: Responsive layout that works perfectly on all devices
- ⚡ **Fast Performance**: Optimized for Cloudflare Pages deployment
- 🎨 **Tailwind CSS**: Modern utility-first CSS framework
- 🌙 **Dark Mode**: Built-in dark/light mode toggle
- 📊 **SEO Optimized**: Structured data, meta tags, and sitemap generation
- 🔍 **Search Functionality**: Built-in search overlay
- 📰 **News-Focused Layout**: Designed for viral content and entertainment news
- 💬 **Social Sharing**: One-click sharing to Facebook, Twitter, LinkedIn
- 📈 **Analytics Ready**: Google Analytics integration
- ♿ **Accessible**: WCAG compliant with proper focus management

## Demo

[Live Demo](https://your-demo-site.pages.dev) (Replace with your demo URL)

## Quick Start

### Prerequisites

- [<PERSON> Extended](https://gohugo.io/getting-started/installing/) (v0.100.0 or later)
- [Node.js](https://nodejs.org/) (v16 or later)
- [Git](https://git-scm.com/)

### Installation

1. **Create a new Hugo site:**
   ```bash
   hugo new site my-news-site
   cd my-news-site
   ```

2. **Clone this theme:**
   ```bash
   git clone https://github.com/yourusername/hugo-coveredgeekly.git themes/coveredgeekly
   ```

3. **Copy the example configuration:**
   ```bash
   cp themes/coveredgeekly/exampleSite/hugo.toml .
   ```

4. **Install dependencies:**
   ```bash
   npm install
   ```

5. **Start the development server:**
   ```bash
   npm run dev
   ```

Your site will be available at `http://localhost:1313`

## Configuration

### Basic Configuration

Update your `hugo.toml` file:

```toml
baseURL = "https://your-site.pages.dev"
languageCode = "en-us"
title = "Your News Site"
theme = "coveredgeekly"

[params]
  description = "Your site description"
  author = "Your Name"
  logo = "/images/logo.png"
  
  # Social media
  facebook = "https://www.facebook.com/yourpage/"
  twitter = "https://twitter.com/youraccount"
  
  # SEO
  images = ["/images/og-image.jpg"]
```

### Menu Configuration

The theme supports multi-level navigation menus:

```toml
[menu]
  [[menu.main]]
    name = "News"
    url = "/categories/news/"
    weight = 10
    
    [[menu.main]]
      name = "Viral News"
      url = "/categories/viral-news/"
      parent = "News"
      weight = 11
```

### Content Structure

Create content using Hugo's content structure:

```
content/
├── _index.md          # Homepage content
├── about/
│   └── _index.md      # About page
└── posts/
    ├── _index.md      # Posts listing page
    └── your-post.md   # Individual posts
```

### Post Front Matter

```yaml
---
title: "Your Post Title"
date: 2024-01-15T10:30:00Z
draft: false
description: "Post description for SEO and social sharing"
categories: ["Entertainment", "Celebrity"]
tags: ["viral", "news", "trending"]
author: "Author Name"
featured_image: "/images/post-image.jpg"
---
```

## Customization

### Colors and Styling

The theme uses Tailwind CSS. Customize colors in `tailwind.config.js`:

```javascript
theme: {
  extend: {
    colors: {
      primary: {
        500: '#your-color',
        // ... other shades
      }
    }
  }
}
```

### Social Media Integration

1. **Facebook Open Graph**: Automatically generated from post metadata
2. **Twitter Cards**: Configured for large image cards
3. **Social Sharing**: Built-in sharing buttons with tracking

### Analytics

Add your Google Analytics ID to `hugo.toml`:

```toml
googleAnalytics = "G-XXXXXXXXXX"
```

## Deployment

### Cloudflare Pages

1. **Connect your repository** to Cloudflare Pages
2. **Build settings:**
   - Build command: `npm run build`
   - Build output directory: `public`
   - Environment variables: `HUGO_VERSION=0.120.0`

3. **Custom domain** (optional): Configure in Cloudflare Pages dashboard

### Other Platforms

- **Netlify**: Use the same build settings
- **Vercel**: Hugo is supported out of the box
- **GitHub Pages**: Use GitHub Actions for deployment

## Content Guidelines

### Writing Viral Content

1. **Headlines**: Use compelling, shareable headlines
2. **Images**: Always include featured images (1200x630px recommended)
3. **Categories**: Use consistent categorization
4. **Tags**: Add relevant tags for discoverability
5. **Social Optimization**: Write descriptions that work well when shared

### SEO Best Practices

1. **Meta Descriptions**: Keep under 160 characters
2. **Image Alt Text**: Always include descriptive alt text
3. **Internal Linking**: Link to related content
4. **URL Structure**: Use clean, descriptive URLs

## Development

### Local Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### CSS Development

The theme uses Tailwind CSS with custom components:

```bash
# Watch CSS changes
npm run build:css

# Build CSS for production
npm run build:css:prod
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

- 📖 [Documentation](https://github.com/yourusername/hugo-coveredgeekly/wiki)
- 🐛 [Issues](https://github.com/yourusername/hugo-coveredgeekly/issues)
- 💬 [Discussions](https://github.com/yourusername/hugo-coveredgeekly/discussions)

## License

This theme is released under the [MIT License](LICENSE).

## Credits

- Inspired by [CoveredGeekly](https://coveredgeekly.com/)
- Built with [Hugo](https://gohugo.io/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Icons from [Heroicons](https://heroicons.com/)

---

**Made with ❤️ for the news and entertainment community**
