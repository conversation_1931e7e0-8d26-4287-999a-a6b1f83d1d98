# GeeklyNews - <PERSON> Site

A complete sample site using a Hugo theme inspired by CoveredGeekly, optimized for viral news and entertainment content with Facebook engagement focus.

## 🎯 What's Included

This is a **ready-to-run sample site** with:

- ✅ **6 Sample Articles** covering viral news, entertainment, gaming, and celebrity content
- ✅ **Complete Theme** with responsive design and social media optimization
- ✅ **Facebook-Optimized** layouts for maximum engagement
- ✅ **Mobile-First** design perfect for social media traffic
- ✅ **Dark Mode** toggle functionality
- ✅ **Social Sharing** buttons on every post
- ✅ **SEO Optimized** with Open Graph and Twitter Cards
- ✅ **Tailwind CSS** for modern styling
- ✅ **Cloudflare Pages** deployment ready

## 🚀 Quick Start (3 Options)

### Option 1: Local Installation (Recommended)

**Step 1: Install Hugo**
```bash
# macOS
brew install hugo

# Windows (with <PERSON>y)
choco install hugo-extended

# Linux
snap install hugo --channel=extended
```

**Step 2: Run the Site**
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

Visit: `http://localhost:1313`

### Option 2: Docker (No Hugo Installation Required)

```bash
# Run with Dock<PERSON> Compose
docker-compose up

# Or run directly
docker run --rm -it -v $(pwd):/src -p 1313:1313 klakegg/hugo:ext-alpine server --bind 0.0.0.0
```

### Option 3: GitHub Codespaces (Cloud Development)

1. Click the "Code" button → "Codespaces" → "Create codespace"
2. Wait for environment to load
3. Run: `npm install && npm run dev`
4. Open the forwarded port

## 📁 What You Get

```
hugofbmag/
├── 📄 6 Sample Posts (ready-to-read viral content)
├── 🎨 Complete Theme (responsive, social-optimized)
├── ⚙️  Configuration (ready for customization)
├── 🚀 Deployment Setup (Cloudflare Pages ready)
└── 📚 Documentation (setup guides and customization)
```

## 📰 Sample Content

The site includes realistic sample posts:

1. **🎬 Celebrity Career Change** - Breaking entertainment news
2. **🦸 Marvel MCU Secret** - Superhero movie rumors and fan reactions
3. **📱 TikTok Viral Trend** - Social media phenomenon breakdown
4. **🎮 Gaming Controversy** - Developer vs. community drama
5. **🐦 Celebrity Twitter Drama** - Social media scandal coverage
6. **📺 Netflix Cancellation** - Fan outrage and organized campaigns

## 🛠️ Easy Customization

**Update Site Info:**
```toml
# Edit hugo.toml
title = "Your Site Name"
[params]
  description = "Your description"
  facebook = "https://facebook.com/yourpage"
```

**Add Your Content:**
```bash
# Create new posts
hugo new posts/your-article.md
```

**Customize Design:**
```javascript
// Edit tailwind.config.js
colors: {
  primary: { 500: '#your-brand-color' }
}
```

## 🐛 Troubleshooting

### "Hugo command not found" Error
This means Hugo isn't installed. Choose one of these solutions:

**🍎 macOS:**
```bash
brew install hugo
```

**🪟 Windows:**
```powershell
choco install hugo-extended
```

**🐧 Linux:**
```bash
snap install hugo --channel=extended
```

**🐳 Use Docker instead:**
```bash
docker-compose up
```

**☁️ Use GitHub Codespaces:**
- No local installation required
- Click "Code" → "Codespaces" → "Create codespace"

### Other Common Issues

**CSS not loading:** Run `npm install` first
**Port already in use:** Try `hugo server --port 1314`
**Permission denied:** Try `chmod +x start-dev.sh`

📖 **Full installation guide:** See [INSTALL.md](INSTALL.md)

## Configuration

### Basic Configuration

Update your `hugo.toml` file:

```toml
baseURL = "https://your-site.pages.dev"
languageCode = "en-us"
title = "Your News Site"
theme = "coveredgeekly"

[params]
  description = "Your site description"
  author = "Your Name"
  logo = "/images/logo.png"
  
  # Social media
  facebook = "https://www.facebook.com/yourpage/"
  twitter = "https://twitter.com/youraccount"
  
  # SEO
  images = ["/images/og-image.jpg"]
```

### Menu Configuration

The theme supports multi-level navigation menus:

```toml
[menu]
  [[menu.main]]
    name = "News"
    url = "/categories/news/"
    weight = 10
    
    [[menu.main]]
      name = "Viral News"
      url = "/categories/viral-news/"
      parent = "News"
      weight = 11
```

### Content Structure

Create content using Hugo's content structure:

```
content/
├── _index.md          # Homepage content
├── about/
│   └── _index.md      # About page
└── posts/
    ├── _index.md      # Posts listing page
    └── your-post.md   # Individual posts
```

### Post Front Matter

```yaml
---
title: "Your Post Title"
date: 2024-01-15T10:30:00Z
draft: false
description: "Post description for SEO and social sharing"
categories: ["Entertainment", "Celebrity"]
tags: ["viral", "news", "trending"]
author: "Author Name"
featured_image: "/images/post-image.jpg"
---
```

## Customization

### Colors and Styling

The theme uses Tailwind CSS. Customize colors in `tailwind.config.js`:

```javascript
theme: {
  extend: {
    colors: {
      primary: {
        500: '#your-color',
        // ... other shades
      }
    }
  }
}
```

### Social Media Integration

1. **Facebook Open Graph**: Automatically generated from post metadata
2. **Twitter Cards**: Configured for large image cards
3. **Social Sharing**: Built-in sharing buttons with tracking

### Analytics

Add your Google Analytics ID to `hugo.toml`:

```toml
googleAnalytics = "G-XXXXXXXXXX"
```

## Deployment

### Cloudflare Pages

1. **Connect your repository** to Cloudflare Pages
2. **Build settings:**
   - Build command: `npm run build`
   - Build output directory: `public`
   - Environment variables: `HUGO_VERSION=0.120.0`

3. **Custom domain** (optional): Configure in Cloudflare Pages dashboard

### Other Platforms

- **Netlify**: Use the same build settings
- **Vercel**: Hugo is supported out of the box
- **GitHub Pages**: Use GitHub Actions for deployment

## Content Guidelines

### Writing Viral Content

1. **Headlines**: Use compelling, shareable headlines
2. **Images**: Always include featured images (1200x630px recommended)
3. **Categories**: Use consistent categorization
4. **Tags**: Add relevant tags for discoverability
5. **Social Optimization**: Write descriptions that work well when shared

### SEO Best Practices

1. **Meta Descriptions**: Keep under 160 characters
2. **Image Alt Text**: Always include descriptive alt text
3. **Internal Linking**: Link to related content
4. **URL Structure**: Use clean, descriptive URLs

## Development

### Local Development

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

### CSS Development

The theme uses Tailwind CSS with custom components:

```bash
# Watch CSS changes
npm run build:css

# Build CSS for production
npm run build:css:prod
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

- 📖 [Documentation](https://github.com/yourusername/hugo-coveredgeekly/wiki)
- 🐛 [Issues](https://github.com/yourusername/hugo-coveredgeekly/issues)
- 💬 [Discussions](https://github.com/yourusername/hugo-coveredgeekly/discussions)

## License

This theme is released under the [MIT License](LICENSE).

## Credits

- Inspired by [CoveredGeekly](https://coveredgeekly.com/)
- Built with [Hugo](https://gohugo.io/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
- Icons from [Heroicons](https://heroicons.com/)

---

**Made with ❤️ for the news and entertainment community**
