# theme.toml template for a Hugo theme
# See https://github.com/gohugoio/hugoThemes#themetoml for an example

name = "CoveredGeekly"
license = "MIT"
licenselink = "https://github.com/yourname/hugo-coveredgeekly/blob/master/LICENSE"
description = "A Hugo theme inspired by CoveredGeekly - optimized for viral news and entertainment content with Facebook engagement focus"
homepage = "http://example.com/"
tags = ["blog", "news", "entertainment", "responsive", "social", "viral"]
features = ["responsive", "social-sharing", "seo-optimized", "tailwind"]
min_version = "0.41.0"

[author]
  name = "Your Name"
  homepage = "http://example.com/"

# If porting an existing theme
[original]
  name = "CoveredGeekly Inspired"
  homepage = "https://coveredgeekly.com/"
  repo = ""
