<!-- Open Graph / Facebook -->
<meta property="og:type" content="{{ if .IsPage }}article{{ else }}website{{ end }}">
<meta property="og:title" content="{{ if .Title }}{{ .Title }}{{ else }}{{ .Site.Title }}{{ end }}">
<meta property="og:description" content="{{ if .Description }}{{ .Description }}{{ else if .Summary }}{{ .Summary }}{{ else }}{{ .Site.Params.description }}{{ end }}">
<meta property="og:url" content="{{ .Permalink }}">
<meta property="og:site_name" content="{{ .Site.Title }}">

{{ if .Params.featured_image }}
<meta property="og:image" content="{{ .Params.featured_image | absURL }}">
{{ else if .Site.Params.images }}
<meta property="og:image" content="{{ index .Site.Params.images 0 | absURL }}">
{{ end }}

{{ if .IsPage }}
<meta property="article:published_time" content="{{ .Date.Format "2006-01-02T15:04:05Z07:00" }}">
<meta property="article:modified_time" content="{{ .Lastmod.Format "2006-01-02T15:04:05Z07:00" }}">
{{ if .Params.author }}
<meta property="article:author" content="{{ .Params.author }}">
{{ end }}
{{ range .Params.categories }}
<meta property="article:section" content="{{ . }}">
{{ end }}
{{ range .Params.tags }}
<meta property="article:tag" content="{{ . }}">
{{ end }}
{{ end }}

<!-- Facebook App ID (optional) -->
{{ if .Site.Params.facebook_app_id }}
<meta property="fb:app_id" content="{{ .Site.Params.facebook_app_id }}">
{{ end }}
