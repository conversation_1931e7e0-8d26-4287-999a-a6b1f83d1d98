{{ define "main" }}
<div class="container mx-auto px-4 py-8">
    <!-- <PERSON>er -->
    <div class="mb-8">
        <h1 class="text-4xl font-black text-gray-900 dark:text-white mb-4">
            {{ if .IsHome }}
                Latest News
            {{ else }}
                {{ .Title }}
            {{ end }}
        </h1>
        {{ if .Description }}
        <p class="text-lg text-gray-600 dark:text-gray-400">{{ .Description }}</p>
        {{ end }}
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-2">
            {{ if .Paginator.Pages }}
                <!-- Featured Article (First Post) -->
                {{ $first := index .Paginator.Pages 0 }}
                {{ if $first }}
                <article class="mb-12 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden card-hover">
                    {{ if $first.Params.featured_image }}
                    <div class="relative">
                        <img src="{{ $first.Params.featured_image }}" alt="{{ $first.Title }}" class="w-full h-64 md:h-80 object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 right-4">
                            {{ if $first.Params.categories }}
                            <div class="mb-2">
                                {{ range $first.Params.categories }}
                                <span class="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded-full mr-2">{{ . }}</span>
                                {{ end }}
                            </div>
                            {{ end }}
                            <h2 class="text-2xl md:text-3xl font-bold text-white mb-2">
                                <a href="{{ $first.Permalink }}" class="hover:text-blue-300 transition-colors">{{ $first.Title }}</a>
                            </h2>
                        </div>
                    </div>
                    {{ end }}
                    <div class="p-6">
                        {{ if not $first.Params.featured_image }}
                        <div class="mb-4">
                            {{ if $first.Params.categories }}
                            <div class="mb-2">
                                {{ range $first.Params.categories }}
                                <span class="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded-full mr-2">{{ . }}</span>
                                {{ end }}
                            </div>
                            {{ end }}
                            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-2">
                                <a href="{{ $first.Permalink }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">{{ $first.Title }}</a>
                            </h2>
                        </div>
                        {{ end }}
                        
                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                            <time datetime="{{ $first.Date.Format "2006-01-02" }}">{{ $first.Date.Format "January 2, 2006" }}</time>
                            {{ if $first.Params.author }}
                            <span class="mx-2">•</span>
                            <span>By {{ $first.Params.author }}</span>
                            {{ end }}
                            {{ if $first.ReadingTime }}
                            <span class="mx-2">•</span>
                            <span>{{ $first.ReadingTime }} min read</span>
                            {{ end }}
                        </div>
                        
                        {{ if $first.Summary }}
                        <p class="text-gray-600 dark:text-gray-300 mb-4">{{ $first.Summary }}</p>
                        {{ end }}
                        
                        <a href="{{ $first.Permalink }}" class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-semibold transition-colors">
                            Read More
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </article>
                {{ end }}

                <!-- Article Grid -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {{ range after 1 .Paginator.Pages }}
                    <article class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden card-hover">
                        {{ if .Params.featured_image }}
                        <div class="relative">
                            <img src="{{ .Params.featured_image }}" alt="{{ .Title }}" class="w-full h-48 object-cover">
                            {{ if .Params.categories }}
                            <div class="absolute top-4 left-4">
                                {{ range first 1 .Params.categories }}
                                <span class="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded-full">{{ . }}</span>
                                {{ end }}
                            </div>
                            {{ end }}
                        </div>
                        {{ end }}
                        
                        <div class="p-6">
                            {{ if not .Params.featured_image }}
                            {{ if .Params.categories }}
                            <div class="mb-2">
                                {{ range first 1 .Params.categories }}
                                <span class="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded-full">{{ . }}</span>
                                {{ end }}
                            </div>
                            {{ end }}
                            {{ end }}
                            
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3">
                                <a href="{{ .Permalink }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">{{ .Title }}</a>
                            </h3>
                            
                            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                                <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "Jan 2" }}</time>
                                {{ if .Params.author }}
                                <span class="mx-2">•</span>
                                <span>{{ .Params.author }}</span>
                                {{ end }}
                            </div>
                            
                            {{ if .Summary }}
                            <p class="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-3">{{ .Summary }}</p>
                            {{ end }}
                            
                            <a href="{{ .Permalink }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-semibold text-sm transition-colors">
                                Read More →
                            </a>
                        </div>
                    </article>
                    {{ end }}
                </div>

                <!-- Pagination -->
                {{ if gt .Paginator.TotalPages 1 }}
                <div class="mt-12 flex justify-center">
                    <nav class="flex items-center space-x-2">
                        {{ if .Paginator.HasPrev }}
                        <a href="{{ .Paginator.Prev.URL }}" class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            Previous
                        </a>
                        {{ end }}
                        
                        {{ range .Paginator.Pagers }}
                        {{ if eq . .Paginator }}
                        <span class="px-4 py-2 bg-blue-600 text-white rounded-lg">{{ .PageNumber }}</span>
                        {{ else }}
                        <a href="{{ .URL }}" class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">{{ .PageNumber }}</a>
                        {{ end }}
                        {{ end }}
                        
                        {{ if .Paginator.HasNext }}
                        <a href="{{ .Paginator.Next.URL }}" class="px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            Next
                        </a>
                        {{ end }}
                    </nav>
                </div>
                {{ end }}
            {{ else }}
                <div class="text-center py-12">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">No articles found</h2>
                    <p class="text-gray-600 dark:text-gray-400">Check back later for new content!</p>
                </div>
            {{ end }}
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            {{ partial "sidebar.html" . }}
        </div>
    </div>
</div>
{{ end }}
