{{- $pages := where .Site.RegularPages "Type" "posts" -}}
{{- $limited := $pages | first 1000 -}}
[
{{- range $index, $page := $limited -}}
{{- if $index -}}, {{- end }}
{
  "title": {{ $page.Title | jsonify }},
  "url": {{ $page.Permalink | jsonify }},
  "date": {{ $page.Date.Format "2006-01-02" | jsonify }},
  "summary": {{ $page.Summary | jsonify }},
  "content": {{ $page.Plain | jsonify }},
  "categories": {{ $page.Params.categories | jsonify }},
  "tags": {{ $page.Params.tags | jsonify }},
  "author": {{ $page.Params.author | jsonify }}
}
{{- end -}}
]
