{{ define "main" }}
<article class="container mx-auto px-4 py-8">
    <!-- Reading Progress Bar -->
    <div class="fixed top-0 left-0 w-full h-1 bg-gray-200 dark:bg-gray-700 z-50">
        <div class="reading-progress h-full bg-blue-600 transition-all duration-300" style="width: 0%"></div>
    </div>

    <div class="max-w-4xl mx-auto">
        <!-- Article Header -->
        <header class="mb-8">
            <!-- Categories -->
            {{ if .Params.categories }}
            <div class="mb-4">
                {{ range .Params.categories }}
                <a href="/categories/{{ . | urlize }}/" class="inline-block bg-blue-600 text-white text-sm px-3 py-1 rounded-full mr-2 hover:bg-blue-700 transition-colors">{{ . }}</a>
                {{ end }}
            </div>
            {{ end }}

            <!-- Title -->
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 dark:text-white mb-6 leading-tight">{{ .Title }}</h1>

            <!-- Meta Information -->
            <div class="flex flex-wrap items-center text-gray-600 dark:text-gray-400 text-sm mb-6">
                <div class="flex items-center mr-6 mb-2">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "January 2, 2006" }}</time>
                </div>
                
                {{ if .Params.author }}
                <div class="flex items-center mr-6 mb-2">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span>By {{ .Params.author }}</span>
                </div>
                {{ end }}
                
                {{ if .ReadingTime }}
                <div class="flex items-center mr-6 mb-2">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>{{ .ReadingTime }} min read</span>
                </div>
                {{ end }}
                
                <div class="flex items-center mb-2">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <span>{{ .WordCount }} words</span>
                </div>
            </div>

            <!-- Social Share Buttons -->
            <div class="flex items-center space-x-4 mb-8">
                <span class="text-sm font-semibold text-gray-700 dark:text-gray-300">Share:</span>
                <button onclick="shareOnFacebook('{{ .Permalink }}', '{{ .Title }}')" class="share-button flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                    </svg>
                    <span>Facebook</span>
                </button>
                
                <button onclick="shareOnTwitter('{{ .Permalink }}', '{{ .Title }}')" class="share-button flex items-center space-x-2 bg-sky-500 text-white px-4 py-2 rounded-lg hover:bg-sky-600 transition-colors">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                    </svg>
                    <span>Twitter</span>
                </button>
                
                <button onclick="shareOnLinkedIn('{{ .Permalink }}', '{{ .Title }}')" class="share-button flex items-center space-x-2 bg-blue-700 text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                    </svg>
                    <span>LinkedIn</span>
                </button>
                
                <button onclick="copyToClipboard('{{ .Permalink }}')" class="share-button flex items-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <span>Copy Link</span>
                </button>
            </div>
        </header>

        <!-- Featured Image -->
        {{ if .Params.featured_image }}
        <div class="mb-8">
            <img src="{{ .Params.featured_image }}" alt="{{ .Title }}" class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg">
        </div>
        {{ end }}

        <!-- Article Content -->
        <div class="prose prose-lg max-w-none dark:prose-invert prose-headings:font-bold prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-a:no-underline hover:prose-a:underline prose-strong:text-gray-900 dark:prose-strong:text-white prose-code:text-blue-600 dark:prose-code:text-blue-400 prose-code:bg-gray-100 dark:prose-code:bg-gray-800 prose-code:px-1 prose-code:py-0.5 prose-code:rounded prose-pre:bg-gray-900 prose-pre:text-gray-100 prose-blockquote:border-l-blue-600 prose-blockquote:bg-blue-50 dark:prose-blockquote:bg-blue-900/20 prose-blockquote:pl-6 prose-blockquote:py-4 prose-blockquote:rounded-r-lg prose-img:rounded-lg prose-img:shadow-lg">
            {{ .Content }}
        </div>

        <!-- Tags -->
        {{ if .Params.tags }}
        <div class="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Tags</h3>
            <div class="flex flex-wrap gap-2">
                {{ range .Params.tags }}
                <a href="/tags/{{ . | urlize }}/" class="inline-block bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-sm px-3 py-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#{{ . }}</a>
                {{ end }}
            </div>
        </div>
        {{ end }}

        <!-- Author Bio -->
        {{ if .Params.author }}
        <div class="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-start space-x-4">
                <div class="w-16 h-16 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ .Params.author }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ if .Params.author_bio }}{{ .Params.author_bio }}{{ else }}Contributing writer at {{ .Site.Title }}{{ end }}</p>
                </div>
            </div>
        </div>
        {{ end }}

        <!-- Related Articles -->
        {{ $related := .Site.RegularPages.Related . | first 3 }}
        {{ if $related }}
        <div class="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Related Articles</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                {{ range $related }}
                <article class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden card-hover">
                    {{ if .Params.featured_image }}
                    <img src="{{ .Params.featured_image }}" alt="{{ .Title }}" class="w-full h-40 object-cover">
                    {{ end }}
                    <div class="p-4">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            <a href="{{ .Permalink }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">{{ .Title }}</a>
                        </h4>
                        <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                            <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "Jan 2, 2006" }}</time>
                        </div>
                        {{ if .Summary }}
                        <p class="text-gray-600 dark:text-gray-300 text-sm line-clamp-2">{{ .Summary }}</p>
                        {{ end }}
                    </div>
                </article>
                {{ end }}
            </div>
        </div>
        {{ end }}
    </div>
</article>
{{ end }}
