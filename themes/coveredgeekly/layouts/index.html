{{ define "main" }}
<div class="container mx-auto px-4 py-8">
    <!-- Hero Section with Featured Articles -->
    <section class="mb-12">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Main Featured Article -->
            {{ $featured := index (first 1 .Site.RegularPages) 0 }}
            {{ if $featured }}
            <article class="lg:col-span-1">
                <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden card-hover">
                    {{ if $featured.Params.featured_image }}
                    <div class="relative">
                        <img src="{{ $featured.Params.featured_image }}" alt="{{ $featured.Title }}" class="w-full h-80 object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent"></div>
                        <div class="absolute bottom-6 left-6 right-6">
                            {{ if $featured.Params.categories }}
                            <div class="mb-3">
                                {{ range first 1 $featured.Params.categories }}
                                <span class="inline-block bg-red-600 text-white text-sm px-3 py-1 rounded-full font-semibold">{{ . }}</span>
                                {{ end }}
                            </div>
                            {{ end }}
                            <h2 class="text-2xl md:text-3xl font-black text-white mb-3 leading-tight">
                                <a href="{{ $featured.Permalink }}" class="hover:text-blue-300 transition-colors">{{ $featured.Title }}</a>
                            </h2>
                            <div class="flex items-center text-white/80 text-sm">
                                <time datetime="{{ $featured.Date.Format "2006-01-02" }}">{{ $featured.Date.Format "January 2, 2006" }}</time>
                                {{ if $featured.Params.author }}
                                <span class="mx-2">•</span>
                                <span>{{ $featured.Params.author }}</span>
                                {{ end }}
                            </div>
                        </div>
                    </div>
                    {{ end }}
                    <div class="p-6">
                        {{ if not $featured.Params.featured_image }}
                        <div class="mb-4">
                            {{ if $featured.Params.categories }}
                            <div class="mb-2">
                                {{ range first 1 $featured.Params.categories }}
                                <span class="inline-block bg-red-600 text-white text-sm px-3 py-1 rounded-full font-semibold">{{ . }}</span>
                                {{ end }}
                            </div>
                            {{ end }}
                            <h2 class="text-2xl md:text-3xl font-black text-gray-900 dark:text-white mb-2">
                                <a href="{{ $featured.Permalink }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">{{ $featured.Title }}</a>
                            </h2>
                        </div>
                        {{ end }}
                        
                        {{ if $featured.Summary }}
                        <p class="text-gray-600 dark:text-gray-300 mb-4 text-lg">{{ $featured.Summary }}</p>
                        {{ end }}
                        
                        <a href="{{ $featured.Permalink }}" class="inline-flex items-center bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                            Read Full Story
                            <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </article>
            {{ end }}

            <!-- Secondary Featured Articles -->
            <div class="lg:col-span-1 space-y-6">
                {{ range after 1 (first 4 .Site.RegularPages) }}
                <article class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden card-hover">
                    <div class="flex">
                        {{ if .Params.featured_image }}
                        <div class="flex-shrink-0">
                            <img src="{{ .Params.featured_image }}" alt="{{ .Title }}" class="w-32 h-24 object-cover">
                        </div>
                        {{ end }}
                        <div class="flex-1 p-4">
                            {{ if .Params.categories }}
                            <div class="mb-2">
                                {{ range first 1 .Params.categories }}
                                <span class="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded-full">{{ . }}</span>
                                {{ end }}
                            </div>
                            {{ end }}
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 line-clamp-2">
                                <a href="{{ .Permalink }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">{{ .Title }}</a>
                            </h3>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "Jan 2" }}</time>
                                {{ if .Params.author }}
                                <span class="mx-1">•</span>
                                <span>{{ .Params.author }}</span>
                                {{ end }}
                            </div>
                        </div>
                    </div>
                </article>
                {{ end }}
            </div>
        </div>
    </section>

    <!-- Trending Section -->
    <section class="mb-12">
        <div class="flex items-center mb-6">
            <h2 class="text-3xl font-black text-gray-900 dark:text-white flex items-center">
                <svg class="w-8 h-8 mr-3 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                </svg>
                Trending Today
            </h2>
            <div class="flex-1 h-px bg-gradient-to-r from-red-500 to-transparent ml-4"></div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {{ range after 4 (first 8 .Site.RegularPages) }}
            <article class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden card-hover">
                {{ if .Params.featured_image }}
                <div class="relative">
                    <img src="{{ .Params.featured_image }}" alt="{{ .Title }}" class="w-full h-40 object-cover">
                    {{ if .Params.categories }}
                    <div class="absolute top-3 left-3">
                        {{ range first 1 .Params.categories }}
                        <span class="inline-block bg-black/70 text-white text-xs px-2 py-1 rounded-full">{{ . }}</span>
                        {{ end }}
                    </div>
                    {{ end }}
                </div>
                {{ end }}
                
                <div class="p-4">
                    {{ if not .Params.featured_image }}
                    {{ if .Params.categories }}
                    <div class="mb-2">
                        {{ range first 1 .Params.categories }}
                        <span class="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded-full">{{ . }}</span>
                        {{ end }}
                    </div>
                    {{ end }}
                    {{ end }}
                    
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2 line-clamp-2">
                        <a href="{{ .Permalink }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">{{ .Title }}</a>
                    </h3>
                    
                    <div class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                        <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "Jan 2" }}</time>
                        {{ if .Params.author }}
                        <span class="mx-1">•</span>
                        <span>{{ .Params.author }}</span>
                        {{ end }}
                    </div>
                    
                    {{ if .Summary }}
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-3 line-clamp-2">{{ .Summary }}</p>
                    {{ end }}
                    
                    <a href="{{ .Permalink }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-semibold text-sm transition-colors">
                        Read More →
                    </a>
                </div>
            </article>
            {{ end }}
        </div>
    </section>

    <!-- Main Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Latest Articles -->
        <div class="lg:col-span-2">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-3xl font-black text-gray-900 dark:text-white">Latest News</h2>
                <a href="/posts/" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-semibold transition-colors">View All →</a>
            </div>
            
            <div class="space-y-6">
                {{ range after 8 (first 15 .Site.RegularPages) }}
                <article class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden card-hover">
                    <div class="md:flex">
                        {{ if .Params.featured_image }}
                        <div class="md:flex-shrink-0">
                            <img src="{{ .Params.featured_image }}" alt="{{ .Title }}" class="w-full md:w-48 h-48 md:h-32 object-cover">
                        </div>
                        {{ end }}
                        <div class="p-6 flex-1">
                            {{ if .Params.categories }}
                            <div class="mb-2">
                                {{ range first 1 .Params.categories }}
                                <span class="inline-block bg-blue-600 text-white text-xs px-2 py-1 rounded-full">{{ . }}</span>
                                {{ end }}
                            </div>
                            {{ end }}
                            
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">
                                <a href="{{ .Permalink }}" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">{{ .Title }}</a>
                            </h3>
                            
                            <div class="text-sm text-gray-500 dark:text-gray-400 mb-3">
                                <time datetime="{{ .Date.Format "2006-01-02" }}">{{ .Date.Format "January 2, 2006" }}</time>
                                {{ if .Params.author }}
                                <span class="mx-2">•</span>
                                <span>{{ .Params.author }}</span>
                                {{ end }}
                                {{ if .ReadingTime }}
                                <span class="mx-2">•</span>
                                <span>{{ .ReadingTime }} min read</span>
                                {{ end }}
                            </div>
                            
                            {{ if .Summary }}
                            <p class="text-gray-600 dark:text-gray-300 mb-4">{{ .Summary }}</p>
                            {{ end }}
                            
                            <a href="{{ .Permalink }}" class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-semibold transition-colors">
                                Continue Reading
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </article>
                {{ end }}
            </div>
            
            <!-- Load More Button -->
            <div class="text-center mt-8">
                <a href="/posts/" class="inline-flex items-center bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    View More Articles
                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
            {{ partial "sidebar.html" . }}
        </div>
    </div>
</div>
{{ end }}
