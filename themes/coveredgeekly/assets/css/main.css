@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }
}

/* Custom component styles */
@layer components {
  .card-hover {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .card-hover:hover {
    @apply transform -translate-y-1 shadow-lg;
  }
  
  .share-button {
    @apply transition-all duration-200 ease-in-out;
  }
  
  .share-button:hover {
    @apply transform scale-105;
  }
  
  .mobile-menu-enter {
    animation: slideDown 0.3s ease-out;
  }
  
  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  /* Line clamp utilities for older browsers */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Loading animation */
  .loading {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700;
  }
  
  /* Reading progress bar */
  .reading-progress {
    @apply transition-all duration-300 ease-out;
  }
  
  /* Social media brand colors */
  .facebook-blue {
    background-color: #1877f2;
  }
  
  .twitter-blue {
    background-color: #1da1f2;
  }
  
  .linkedin-blue {
    background-color: #0077b5;
  }
  
  /* Custom prose styles for article content */
  .prose {
    @apply max-w-none;
  }
  
  .prose img {
    @apply rounded-lg shadow-md;
  }
  
  .prose blockquote {
    @apply border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 pl-6 py-4 rounded-r-lg;
  }
  
  .prose code {
    @apply bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm;
  }
  
  .prose pre {
    @apply bg-gray-900 text-gray-100 rounded-lg;
  }
  
  .prose pre code {
    @apply bg-transparent p-0;
  }
  
  /* Custom button styles */
  .btn-primary {
    @apply bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200;
  }
  
  .btn-outline {
    @apply border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors duration-200;
  }
}

/* Custom utility styles */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* Focus styles for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800;
  }
  
  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Custom animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }
  
  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    @apply text-black bg-white;
  }
  
  a {
    @apply text-black no-underline;
  }
  
  .prose {
    @apply text-black;
  }
}
