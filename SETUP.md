# GeeklyNews Sample Site Setup Guide

This is a complete sample site using the CoveredGeekly-inspired Hugo theme, ready to run locally or deploy to Cloudflare Pages.

## 🚀 Quick Start

### Prerequisites
- [<PERSON> Extended](https://gohugo.io/installation/) (v0.100.0 or later)
- [Node.js](https://nodejs.org/) (v16 or later)
- [Git](https://git-scm.com/)

### 1. Install Dependencies
```bash
npm install
```

### 2. Start Development Server
```bash
# Option 1: Use the provided script
chmod +x start-dev.sh
./start-dev.sh

# Option 2: Use npm script
npm run dev

# Option 3: Use Hugo directly
hugo server --disableFastRender
```

### 3. View Your Site
Open your browser to: `http://localhost:1313`

## 📁 Site Structure

```
hugofbmag/
├── content/
│   ├── _index.md              # Homepage content
│   ├── about/
│   │   └── _index.md          # About page
│   └── posts/                 # Blog posts
│       ├── _index.md          # Posts listing
│       ├── sample-viral-news-post.md
│       ├── marvel-actor-surprise-announcement.md
│       ├── viral-tiktok-trend-explained.md
│       ├── gaming-controversy-explodes.md
│       ├── celebrity-twitter-meltdown.md
│       └── netflix-show-cancelled-outrage.md
├── themes/coveredgeekly/      # Theme files
├── static/                    # Static assets
├── hugo.toml                  # Site configuration
├── package.json               # Node.js dependencies
└── tailwind.config.js         # Tailwind CSS config
```

## 🎨 Sample Content

The site includes 6 sample posts covering different content types:

1. **Celebrity News** - Breaking entertainment stories
2. **Marvel/MCU** - Superhero movie news and rumors
3. **Viral Trends** - Social media phenomena and TikTok trends
4. **Gaming** - Video game industry controversies
5. **Celebrity Drama** - Social media scandals and meltdowns
6. **TV/Streaming** - Netflix cancellations and fan reactions

## 🛠️ Customization

### Update Site Information
Edit `hugo.toml`:
```toml
title = "Your Site Name"
[params]
  description = "Your site description"
  author = "Your Name"
  facebook = "https://facebook.com/yourpage"
  twitter = "https://twitter.com/youraccount"
```

### Add Your Logo
Replace the placeholder logo URL in `hugo.toml`:
```toml
[params]
  logo = "/images/your-logo.png"
```

### Customize Colors
Edit `tailwind.config.js` to change the color scheme:
```javascript
colors: {
  primary: {
    500: '#your-brand-color',
    // ... other shades
  }
}
```

### Add Real Images
Replace placeholder images in:
- `static/images/` directory
- Post front matter `featured_image` fields
- Site configuration `images` array

## 📝 Creating New Content

### New Blog Post
```bash
hugo new posts/your-post-title.md
```

### Post Front Matter Template
```yaml
---
title: "Your Post Title"
date: 2024-01-20T10:30:00Z
draft: false
description: "SEO description for social sharing"
categories: ["Entertainment", "Viral News"]
tags: ["trending", "celebrity", "viral"]
author: "Author Name"
featured_image: "https://picsum.photos/1200/630"
---
```

### Content Categories
The theme supports these main categories:
- **Entertainment** (Celebrity, TV & Film, Music, Gaming)
- **News** (Viral News, Technology, Science, Animals, Sport, Crime)
- **Lifestyle** (Food and Drink, Health, Travel)
- **Quizzes** (Personality Tests, Trivia Quizzes)
- **Lists**

## 🚀 Deployment

### Cloudflare Pages (Recommended)
1. Push your code to GitHub/GitLab
2. Connect repository to Cloudflare Pages
3. Build settings:
   - **Build command**: `npm run build`
   - **Build output directory**: `public`
   - **Environment variables**: `HUGO_VERSION=0.120.0`

### Other Platforms
- **Netlify**: Same build settings as Cloudflare
- **Vercel**: Hugo supported out of the box
- **GitHub Pages**: Use GitHub Actions

## 🎯 SEO & Social Media

The theme is optimized for:
- **Facebook Open Graph** tags
- **Twitter Card** meta tags
- **Structured data** (JSON-LD)
- **Social sharing** buttons
- **Mobile-first** responsive design

## 📊 Analytics

Add Google Analytics to `hugo.toml`:
```toml
googleAnalytics = "G-XXXXXXXXXX"
```

## 🔧 Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Build CSS only
npm run build:css

# Clean build files
npm run clean
```

## 🎨 Theme Features

- ✅ **Responsive Design** - Mobile-first approach
- ✅ **Dark Mode** - Built-in theme toggle
- ✅ **Social Sharing** - Facebook, Twitter, LinkedIn
- ✅ **SEO Optimized** - Meta tags, structured data
- ✅ **Fast Performance** - Optimized for Cloudflare Pages
- ✅ **Viral Content Layout** - Designed for engagement
- ✅ **Tailwind CSS** - Modern utility-first styling
- ✅ **Search Functionality** - Built-in search overlay

## 🐛 Troubleshooting

### Hugo Server Won't Start
- Check Hugo version: `hugo version`
- Ensure you have Hugo Extended
- Try: `hugo server --disableFastRender`

### CSS Not Loading
- Run: `npm run build:css`
- Check Tailwind config
- Verify file paths

### Images Not Showing
- Check image URLs in post front matter
- Verify images exist in `static/images/`
- Use absolute URLs for external images

## 📚 Resources

- [Hugo Documentation](https://gohugo.io/documentation/)
- [Tailwind CSS Docs](https://tailwindcss.com/docs)
- [Cloudflare Pages Docs](https://developers.cloudflare.com/pages/)
- [Theme Repository](https://github.com/yourusername/hugo-coveredgeekly)

## 🆘 Support

If you need help:
1. Check the [troubleshooting section](#troubleshooting)
2. Review Hugo and Tailwind documentation
3. Open an issue on the theme repository
4. Join the Hugo community forums

---

**Happy blogging! 🎉**
