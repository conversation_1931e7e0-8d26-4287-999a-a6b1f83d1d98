baseURL = "https://your-site.pages.dev"
languageCode = "en-us"
title = "CoveredGeekly"
theme = "coveredgeekly"

# Site parameters
[params]
  description = "Viral News, Pop Culture, TV & Film – Stay Covered"
  author = "CoveredGeekly"
  logo = "/images/logo.png"
  favicon = "/images/favicon.ico"
  
  # Social media
  facebook = "https://www.facebook.com/CoveredGeeklyOfficial/"
  twitter = "https://twitter.com/CoveredGeekly"
  
  # SEO and social sharing
  images = ["/images/og-image.jpg"]
  
  # Homepage settings
  postsPerPage = 10
  showReadingTime = true
  showShareButtons = true
  
  # Comments (optional)
  disqusShortname = ""

# Menu configuration
[menu]
  [[menu.main]]
    name = "News"
    url = "/categories/news/"
    weight = 10
    
    [[menu.main]]
      name = "Viral News"
      url = "/categories/viral-news/"
      parent = "News"
      weight = 11
      
    [[menu.main]]
      name = "Technology"
      url = "/categories/technology/"
      parent = "News"
      weight = 12
      
    [[menu.main]]
      name = "Science"
      url = "/categories/science/"
      parent = "News"
      weight = 13
      
    [[menu.main]]
      name = "Animals"
      url = "/categories/animals/"
      parent = "News"
      weight = 14
      
    [[menu.main]]
      name = "Sport"
      url = "/categories/sport/"
      parent = "News"
      weight = 15
      
    [[menu.main]]
      name = "Crime"
      url = "/categories/crime/"
      parent = "News"
      weight = 16

  [[menu.main]]
    name = "Entertainment"
    url = "/categories/entertainment/"
    weight = 20
    
    [[menu.main]]
      name = "Celebrity"
      url = "/categories/celebrity/"
      parent = "Entertainment"
      weight = 21
      
    [[menu.main]]
      name = "TV & Film"
      url = "/categories/tv-film/"
      parent = "Entertainment"
      weight = 22
      
    [[menu.main]]
      name = "Music"
      url = "/categories/music/"
      parent = "Entertainment"
      weight = 23
      
    [[menu.main]]
      name = "Gaming"
      url = "/categories/gaming/"
      parent = "Entertainment"
      weight = 24

  [[menu.main]]
    name = "Lifestyle"
    url = "/categories/lifestyle/"
    weight = 30
    
    [[menu.main]]
      name = "Food and Drink"
      url = "/categories/food-drink/"
      parent = "Lifestyle"
      weight = 31
      
    [[menu.main]]
      name = "Health"
      url = "/categories/health/"
      parent = "Lifestyle"
      weight = 32
      
    [[menu.main]]
      name = "Travel"
      url = "/categories/travel/"
      parent = "Lifestyle"
      weight = 33

  [[menu.main]]
    name = "Quizzes"
    url = "/categories/quizzes/"
    weight = 40
    
    [[menu.main]]
      name = "Personality Tests"
      url = "/categories/personality-tests/"
      parent = "Quizzes"
      weight = 41
      
    [[menu.main]]
      name = "Trivia Quizzes"
      url = "/categories/trivia-quizzes/"
      parent = "Quizzes"
      weight = 42

  [[menu.main]]
    name = "Lists"
    url = "/categories/lists/"
    weight = 50

  [[menu.main]]
    name = "About"
    url = "/about/"
    weight = 60

# Markup configuration
[markup]
  [markup.goldmark]
    [markup.goldmark.renderer]
      unsafe = true
  [markup.highlight]
    style = "github"
    lineNos = true

# Build configuration
[build]
  writeStats = true

# Taxonomies
[taxonomies]
  category = "categories"
  tag = "tags"
  author = "authors"

# Output formats
[outputs]
  home = ["HTML", "RSS", "JSON"]
  page = ["HTML"]
  section = ["HTML", "RSS"]

# Privacy configuration
[privacy]
  [privacy.googleAnalytics]
    disable = false
    anonymizeIP = true
    respectDoNotTrack = true
  [privacy.youtube]
    disable = false
    privacyEnhanced = true
