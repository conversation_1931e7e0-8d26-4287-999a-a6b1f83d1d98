# Hugo Best Practices

This document contains advanced Hugo techniques and best practices that can significantly improve your site's performance, maintainability, and SEO.

## Performance & Build Optimization

### 1. Use `.RenderString` for Dynamic Content

Process markdown content dynamically within templates:

```go
{{ $content := "**Bold text** with [links](https://example.com)" }}
{{ $content | .RenderString }}
```

### 2. Cache Expensive Operations

Use `partialCached` to avoid recalculating expensive operations:

```go
{{ $posts := .Site.RegularPages | where "Type" "posts" }}
{{ $cached := partialCached "post-list.html" $posts $posts }}
```

### 3. Optimize Image Processing

Leverage <PERSON>'s built-in image processing:

```go
{{ $image := .Resources.GetMatch "featured.jpg" }}
{{ $resized := $image.Resize "800x webp" }}
{{ $thumbnail := $image.Fill "300x200 webp" }}
```

## Content Organization

### 4. Use Page Bundles for Better Organization

Organize related content together:

```
content/
├── posts/
│   ├── my-post/
│   │   ├── index.md
│   │   ├── featured.jpg
│   │   └── gallery/
│   │       ├── image1.jpg
│   │       └── image2.jpg
```

### 5. Leverage Front Matter Cascading

Set default values for entire sections:

```yaml
# content/posts/_index.md
---
cascade:
  type: posts
  layout: single
  author: "Default Author"
---
```

## Advanced Templating

### 6. Use `with` to Avoid Repetition

Check for existence and assign context in one step:

```go
{{ with .Params.author }}
  <span class="author">By {{ . }}</span>
{{ end }}

{{ with .Params.tags }}
  <div class="tags">
    {{ range . }}
      <span class="tag">{{ . }}</span>
    {{ end }}
  </div>
{{ end }}
```

### 7. Create Reusable Shortcodes with Parameters

Build flexible shortcodes for common patterns:

```go
<!-- layouts/shortcodes/figure.html -->
<figure{{ with .Get "class" }} class="{{ . }}"{{ end }}>
  <img src="{{ .Get "src" }}" alt="{{ .Get "alt" }}">
  {{ with .Get "caption" }}
    <figcaption>{{ . }}</figcaption>
  {{ end }}
</figure>
```

Usage:
```markdown
{{< figure src="image.jpg" alt="Description" class="custom-class" caption="Image caption" >}}
```

### 8. Use `partialCached` for Expensive Partials

Cache partials that don't change often:

```go
{{ partialCached "sidebar.html" . .Section }}
{{ partialCached "navigation.html" . }}
```

## SEO & Structure

### 9. Implement Proper JSON-LD Structured Data

Add structured data for better SEO:

```go
{{ if .IsPage }}
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Article",
  "headline": "{{ .Title }}",
  "author": {
    "@type": "Person",
    "name": "{{ .Params.author }}"
  },
  "datePublished": "{{ .Date.Format "2006-01-02T15:04:05Z07:00" }}",
  "dateModified": "{{ .Lastmod.Format "2006-01-02T15:04:05Z07:00" }}"
}
</script>
{{ end }}
```

### 10. Use Hugo's Built-in Minification

Enable minification in your configuration:

```toml
[minify]
  disableCSS = false
  disableHTML = false
  disableJS = false
  disableJSON = false
  disableSVG = false
  disableXML = false
```

## Additional Tips

### 11. Optimize Build Times

Use `--gc` flag to clean up unused cache:

```bash
hugo --gc
```

### 12. Environment-Specific Configuration

Use different configs for development and production:

```toml
# config/production/hugo.toml
baseURL = "https://yoursite.com"
buildDrafts = false

# config/development/hugo.toml
baseURL = "http://localhost:1313"
buildDrafts = true
```

### 13. Use Hugo Modules for Theme Management

Initialize and manage themes with Hugo modules:

```bash
hugo mod init github.com/yourusername/yoursite
hugo mod get github.com/yourusername/hugomag
```

### 14. Implement Proper Error Handling

Check for resource existence before processing:

```go
{{ $image := .Resources.GetMatch "featured.*" }}
{{ if $image }}
  {{ $resized := $image.Resize "800x" }}
  <img src="{{ $resized.RelPermalink }}" alt="{{ .Title }}">
{{ end }}
```

### 15. Use Hugo's Built-in Analytics

Track build performance:

```bash
hugo --templateMetrics
hugo --templateMetricsHints
```

These practices will help you build faster, more maintainable Hugo sites with better SEO and user experience.