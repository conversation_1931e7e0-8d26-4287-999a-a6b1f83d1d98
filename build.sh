#!/bin/bash

# Build script for Cloudflare Pages deployment

set -e

echo "🚀 Starting Hugo build process..."

# Install Hugo if not present
if ! command -v hugo &> /dev/null; then
    echo "📦 Installing Hugo..."
    wget -O hugo.tar.gz https://github.com/gohugoio/hugo/releases/download/v0.120.0/hugo_extended_0.120.0_Linux-64bit.tar.gz
    tar -xzf hugo.tar.gz
    chmod +x hugo
    export PATH=$PATH:$(pwd)
fi

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm ci

# Build Tailwind CSS
echo "🎨 Building Tailwind CSS..."
npm run build:css:prod

# Build Hugo site
echo "🏗️ Building Hugo site..."
hugo --minify --environment production

echo "✅ Build completed successfully!"

# Display build info
echo "📊 Build information:"
echo "Hugo version: $(hugo version)"
echo "Node version: $(node --version)"
echo "Build directory: public/"
echo "Total files: $(find public -type f | wc -l)"
echo "Total size: $(du -sh public | cut -f1)"

echo "🎉 Ready for deployment!"
