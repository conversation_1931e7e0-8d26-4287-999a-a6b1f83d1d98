# Hugo
/public/
/resources/
.hugo_build.lock

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Compiled CSS (generated by Tailwind)
/static/css/style.css

# Temporary files
*.tmp
*.temp

# Hugo cache
.hugo_build.lock

# Local development
.local/

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Image thumbnails
*.thumb

# Cloudflare Pages
.vercel
.netlify

# Local configuration overrides
hugo.local.toml
config.local.toml
