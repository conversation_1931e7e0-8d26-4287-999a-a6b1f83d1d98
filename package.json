{"name": "hugo-<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "A Hugo theme inspired by CoveredGeekly - optimized for viral news and entertainment content", "main": "index.js", "scripts": {"dev": "hugo server --disableF<PERSON><PERSON><PERSON> --navigateToChanged", "build": "hugo --minify", "build:css": "tailwindcss -i ./themes/coveredgeekly/assets/css/main.css -o ./static/css/style.css --watch", "build:css:prod": "tailwindcss -i ./themes/coveredgeekly/assets/css/main.css -o ./static/css/style.css --minify", "preview": "hugo server --environment production --disableFastRender", "clean": "rm -rf public resources"}, "keywords": ["hugo", "theme", "blog", "news", "entertainment", "viral", "tailwindcss", "responsive"], "author": "Your Name", "license": "MIT", "devDependencies": {"@tailwindcss/typography": "^0.5.10", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/line-clamp": "^0.4.4", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16"}, "dependencies": {}, "repository": {"type": "git", "url": "https://github.com/yourusername/hugo-coveredgeekly.git"}, "bugs": {"url": "https://github.com/yourusername/hugo-coveredgeekly/issues"}, "homepage": "https://github.com/yourusername/hugo-coveredgeekly#readme"}