{"htmlElements": {"tags": ["a", "article", "aside", "blockquote", "body", "button", "div", "em", "footer", "form", "h1", "h2", "h3", "h4", "head", "header", "hr", "html", "img", "input", "li", "link", "main", "meta", "nav", "ol", "p", "path", "script", "section", "span", "strong", "style", "svg", "time", "title", "ul"], "classes": ["absolute", "backdrop-blur-sm", "bg-black", "bg-black/70", "bg-blue-600", "bg-blue-700", "bg-gradient-to-br", "bg-gradient-to-r", "bg-gradient-to-t", "bg-gray-100", "bg-gray-200", "bg-gray-300", "bg-gray-600", "bg-gray-800", "bg-gray-900", "bg-opacity-50", "bg-red-600", "bg-sky-500", "bg-white", "bg-white/10", "block", "border", "border-gray-200", "border-gray-300", "border-gray-700", "border-gray-800", "border-t", "border-white/20", "bottom-4", "bottom-6", "card-hover", "container", "dark:bg-gray-600", "dark:bg-gray-700", "dark:bg-gray-800", "dark:bg-gray-900", "dark:block", "dark:border-gray-600", "dark:border-gray-700", "dark:hidden", "dark:hover:bg-blue-900", "dark:hover:bg-gray-600", "dark:hover:bg-gray-700", "dark:hover:text-blue-300", "dark:hover:text-blue-400", "dark:hover:text-gray-200", "dark:hover:text-sky-400", "dark:prose-a:text-blue-400", "dark:prose-blockquote:bg-blue-900/20", "dark:prose-code:bg-gray-800", "dark:prose-code:text-blue-400", "dark:prose-headings:text-white", "dark:prose-invert", "dark:prose-p:text-gray-300", "dark:prose-strong:text-white", "dark:text-blue-400", "dark:text-gray-300", "dark:text-gray-400", "dark:text-white", "duration-200", "duration-300", "fixed", "flex", "flex-1", "flex-col", "flex-shrink-0", "flex-wrap", "focus:border-transparent", "focus:ring-2", "focus:ring-blue-500", "focus:ring-white/50", "font-black", "font-bold", "font-medium", "font-semibold", "from-black/50", "from-black/70", "from-blue-600", "from-red-500", "gap-2", "gap-6", "gap-8", "grid", "grid-cols-1", "group", "group-hover:opacity-100", "group-hover:visible", "h-1", "h-10", "h-16", "h-24", "h-4", "h-40", "h-48", "h-5", "h-6", "h-64", "h-8", "h-80", "h-full", "h-px", "hidden", "hover:bg-blue-100", "hover:bg-blue-50", "hover:bg-blue-700", "hover:bg-blue-800", "hover:bg-gray-100", "hover:bg-gray-200", "hover:bg-gray-50", "hover:bg-gray-700", "hover:bg-sky-600", "hover:prose-a:underline", "hover:text-blue-300", "hover:text-blue-400", "hover:text-blue-600", "hover:text-blue-800", "hover:text-gray-700", "hover:text-sky-500", "hover:text-white", "inline", "inline-block", "inline-flex", "inset-0", "invisible", "items-center", "items-start", "justify-between", "justify-center", "leading-tight", "left-0", "left-3", "left-4", "left-6", "lg:col-span-1", "lg:col-span-2", "lg:flex", "lg:grid-cols-2", "lg:grid-cols-3", "lg:grid-cols-4", "lg:hidden", "lg:text-5xl", "line-clamp-2", "line-clamp-3", "max-w-2xl", "max-w-4xl", "max-w-none", "mb-1", "mb-12", "mb-2", "mb-3", "mb-4", "mb-6", "mb-8", "md:block", "md:flex-row", "md:grid-cols-2", "md:grid-cols-3", "md:h-80", "md:h-96", "md:mt-0", "md:text-3xl", "md:text-4xl", "min-h-screen", "min-w-0", "ml-1", "ml-2", "ml-4", "mr-2", "mr-3", "mr-6", "mt-12", "mt-16", "mt-2", "mt-4", "mt-8", "mx-1", "mx-2", "mx-auto", "object-cover", "opacity-0", "overflow-hidden", "p-2", "p-4", "p-6", "placeholder-blue-200", "placeholder-gray-400", "prose", "prose-a:no-underline", "prose-a:text-blue-600", "prose-blockquote:bg-blue-50", "prose-blockquote:border-l-blue-600", "prose-blockquote:pl-6", "prose-blockquote:py-4", "prose-blockquote:rounded-r-lg", "prose-code:bg-gray-100", "prose-code:px-1", "prose-code:py-0.5", "prose-code:rounded", "prose-code:text-blue-600", "prose-headings:font-bold", "prose-headings:text-gray-900", "prose-img:rounded-lg", "prose-img:shadow-lg", "prose-lg", "prose-p:text-gray-700", "prose-pre:bg-gray-900", "prose-pre:text-gray-100", "prose-strong:text-gray-900", "pt-8", "px-2", "px-3", "px-4", "px-6", "px-8", "py-1", "py-12", "py-2", "py-3", "py-4", "py-8", "reading-progress", "relative", "right-4", "right-6", "rounded", "rounded-full", "rounded-lg", "rounded-md", "shadow-lg", "shadow-sm", "shadow-xl", "share-button", "space-x-1", "space-x-2", "space-x-3", "space-x-4", "space-x-6", "space-x-8", "space-y-2", "space-y-3", "space-y-4", "space-y-6", "space-y-8", "sticky", "text-2xl", "text-3xl", "text-4xl", "text-blue-100", "text-blue-200", "text-blue-500", "text-blue-600", "text-center", "text-gray-400", "text-gray-500", "text-gray-600", "text-gray-700", "text-gray-900", "text-green-500", "text-lg", "text-purple-500", "text-red-500", "text-sm", "text-white", "text-white/80", "text-xl", "text-xs", "text-yellow-500", "to-blue-800", "to-transparent", "top-0", "top-3", "top-4", "top-full", "tracking-wide", "transition-all", "transition-colors", "uppercase", "via-black/20", "w-16", "w-32", "w-4", "w-48", "w-5", "w-6", "w-8", "w-auto", "w-full", "z-50"], "ids": ["audience-behavior", "behavioral-analysis", "behavioral-patterns", "best-case-for-fans", "best-case-scenario", "brands-jumping-in", "celebrity-reactions", "celebrity-support", "celebrity-support-pours-in", "community-response-strategies", "competitor-platforms-circle", "competitor-studios-capitalize", "creator-concerns", "damage-control-attempts", "developer-player-relationship", "digital-campaigns", "engagement-metrics", "expert-analysis", "expert-predictions", "fan-outrage-reaches-new-heights", "fan-reactions-are-brutal", "fan-reactions-are-wild", "fan-reactions-pour-in", "fan-theories-about-the-real-reason", "fan-theories-and-speculation", "fan-theories-getting-crazy", "how-it-started", "how-to-do-it-if-you-must", "immediate-backlash", "industry-impact", "industry-insider-perspective", "industry-insiders-speak", "industry-predictions", "industry-reactions", "international-variations", "legal-implications", "lessons-for-other-celebrities", "lessons-for-other-developers", "long-term", "main", "market-dynamics", "mobile-menu", "mobile-menu-toggle", "ongoing-issues", "organized-campaigns", "phase-1-awareness", "phase-2-economic-pressure", "phase-3-alternative-solutions", "photo-evidence", "player-power-in-2024", "professional-consequences", "real-world-actions", "recent-casualties", "recent-examples", "recent-similar-moves", "search-close", "search-overlay", "search-toggle", "short-term", "social-media-explosion", "strategy-1-complete-denial", "strategy-2-partial-admission", "strategy-3-full-confession-current", "technical-analysis", "the-backlash-begins", "the-bigger-picture", "the-bottom-line", "the-broader-impact", "the-broader-industry-impact", "the-cancellation-announcement", "the-cast-speaks-out", "the-comments-that-started-it-all", "the-cover-up-attempts", "the-cultural-impact", "the-deletion-frenzy", "the-discovery-that-started-everything", "the-economic-impact", "the-environmental-mission", "the-evidence-was-overwhelming", "the-fan-community-response", "the-fan-investigation-methods", "the-fan-perspective", "the-financial-reality", "the-marvel-studios-response", "the-meltdown-begins", "the-memes-are-endless", "the-memes-are-incredible", "the-memes-are-savage", "the-most-damaging-tweets", "the-netflix-formula", "the-numbers-are-insane", "the-numbers-dont-add-up", "the-numbers-dont-lie", "the-numbers-game", "the-numbers-that-made-it-special", "the-pattern-of-cancellations", "the-psychology-behind-it", "the-psychology-behind-secret-accounts", "the-psychology-of-fan-investment", "the-road-ahead", "the-save-campaign-strategy", "the-science-of-viral-trends", "the-secret-accounts-content", "the-show-that-captured-hearts", "the-slip-that-broke-the-internet", "the-social-media-explosion", "the-studios-damage-control", "the-unexpected-announcement", "the-waiting-game", "theme-toggle", "theory-1-budget-cuts", "theory-2-algorithm-obsession", "theory-3-corporate-politics", "theory-4-streaming-wars-strategy", "timing-coincidences", "viewership-data", "what-exactly-is-this-trend", "what-happens-next", "what-industry-experts-are-saying", "what-other-celebrities-are-doing", "what-other-platforms-are-learning", "what-players-are-really-mad-about", "what-success-looks-like", "what-this-could-mean", "what-this-means-for-fans", "what-this-means-for-gaming", "what-this-means-for-upcoming-projects", "what-we-know-so-far", "whats-next", "why-its-so-addictive-to-watch", "why-we-cant-stop-watching", "worst-case", "worst-case-scenario"]}}