# Installation Guide

This guide will help you install <PERSON> and get the sample site running.

## 🚀 Quick Installation

### macOS (Recommended)
```bash
# Install Homebrew if you don't have it
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Hugo Extended
brew install hugo
```

### Windows
**Option 1: Chocolatey (Recommended)**
```powershell
# Install Chocolatey if you don't have it
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install Hugo Extended
choco install hugo-extended
```

**Option 2: Direct Download**
1. Go to [Hugo Releases](https://github.com/gohugoio/hugo/releases)
2. Download `hugo_extended_X.X.X_windows-amd64.zip`
3. Extract to a folder (e.g., `C:\Hugo\bin`)
4. Add the folder to your PATH environment variable

### Linux
**Ubuntu/Debian:**
```bash
# Install Hugo Extended via Snap (recommended)
sudo snap install hugo --channel=extended

# Or install via apt (may be older version)
sudo apt update
sudo apt install hugo
```

**Other Linux distributions:**
```bash
# Download and install manually
wget https://github.com/gohugoio/hugo/releases/download/v0.120.0/hugo_extended_0.120.0_linux-amd64.tar.gz
tar -xzf hugo_extended_0.120.0_linux-amd64.tar.gz
sudo mv hugo /usr/local/bin/
```

## ✅ Verify Installation

After installation, verify Hugo is working:

```bash
# Check Hugo version
hugo version

# Should show something like:
# hugo v0.120.0+extended darwin/amd64 BuildDate=2023-11-08T11:18:07Z VendorInfo=brew
```

**Important:** Make sure you see `+extended` in the version output. The theme requires Hugo Extended.

## 🏃‍♂️ Run the Sample Site

Once Hugo is installed:

```bash
# Install Node.js dependencies
npm install

# Start the development server
npm run dev

# Or use the provided script
chmod +x start-dev.sh
./start-dev.sh
```

Visit: `http://localhost:1313`

## 🐛 Troubleshooting

### "Hugo command not found"
- Make sure Hugo is in your PATH
- Restart your terminal after installation
- Try the full path: `/usr/local/bin/hugo version`

### "Unable to locate config file"
- Make sure you're in the correct directory (should contain `hugo.toml`)
- Check that `hugo.toml` exists and is readable

### "This feature is not available in your current Hugo version"
- You need Hugo Extended, not the standard version
- Reinstall with the extended version

### CSS not loading properly
- Make sure you have Node.js installed
- Run `npm install` to install Tailwind CSS
- The theme uses Tailwind CSS which requires Node.js

## 🔄 Alternative: Using Docker

If you prefer not to install Hugo locally, you can use Docker:

```bash
# Pull Hugo Docker image
docker pull klakegg/hugo:ext-alpine

# Run the site
docker run --rm -it \
  -v $(pwd):/src \
  -p 1313:1313 \
  klakegg/hugo:ext-alpine \
  server --bind 0.0.0.0
```

## 🌐 Alternative: Using GitHub Codespaces

1. Fork this repository on GitHub
2. Click "Code" → "Codespaces" → "Create codespace"
3. Wait for the environment to load
4. Run: `npm install && npm run dev`
5. Open the forwarded port when prompted

## 📱 Alternative: Using Gitpod

1. Go to: `https://gitpod.io/#https://github.com/yourusername/your-repo`
2. Wait for the workspace to load
3. Run: `npm install && npm run dev`
4. Open the preview when prompted

## 🆘 Still Having Issues?

1. **Check the requirements:**
   - Hugo Extended v0.100.0+
   - Node.js v16+
   - Git

2. **Common solutions:**
   - Restart your terminal
   - Check your PATH environment variable
   - Try running commands with `sudo` (Linux/macOS)
   - Disable antivirus temporarily (Windows)

3. **Get help:**
   - [Hugo Installation Docs](https://gohugo.io/installation/)
   - [Hugo Community Forum](https://discourse.gohugo.io/)
   - [GitHub Issues](https://github.com/gohugoio/hugo/issues)

## 🎉 Next Steps

Once you have the site running:

1. **Customize the content** in `content/posts/`
2. **Update site settings** in `hugo.toml`
3. **Modify the theme** in `themes/coveredgeekly/`
4. **Add your own images** to `static/images/`
5. **Deploy to Cloudflare Pages** or your preferred host

Happy coding! 🚀
