# Security headers for Cloudflare Pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Cache static assets
/css/*
  Cache-Control: public, max-age=31536000, immutable

/js/*
  Cache-Control: public, max-age=31536000, immutable

/images/*
  Cache-Control: public, max-age=31536000, immutable

/fonts/*
  Cache-Control: public, max-age=31536000, immutable

# Cache HTML with shorter duration
/*.html
  Cache-Control: public, max-age=3600

# RSS and sitemap
/index.xml
  Cache-Control: public, max-age=3600
  Content-Type: application/rss+xml; charset=utf-8

/sitemap.xml
  Cache-Control: public, max-age=86400
  Content-Type: application/xml; charset=utf-8
